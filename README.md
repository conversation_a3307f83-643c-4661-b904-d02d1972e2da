# Hugging Face Text Classification Examples

This repository contains comprehensive Python examples for text classification using Hugging Face transformers.

## Files Overview

- `text_classification.py` - Complete text classification class with advanced features
- `simple_example.py` - Simple examples to get started quickly
- `requirements.txt` - Required Python packages

## Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

Or install minimal dependencies:
```bash
pip install transformers torch
```

### 2. Run Simple Examples

```bash
python simple_example.py
```

### 3. Run Advanced Examples

```bash
python text_classification.py
```

## Features

### Simple Usage (simple_example.py)
- Basic sentiment analysis
- Batch text classification
- Comparison of different models

### Advanced Usage (text_classification.py)
- Pre-trained model classification
- Custom model loading
- Fine-tuning capabilities
- Batch processing
- Multiple classification approaches

## Usage Examples

### Basic Sentiment Analysis
```python
from transformers import pipeline

classifier = pipeline("sentiment-analysis")
result = classifier("I love this product!")
print(result)  # [{'label': 'POSITIVE', 'score': 0.999}]
```

### Using the TextClassifier Class
```python
from text_classification import TextClassifier

# Initialize classifier
classifier = TextClassifier()

# Classify single text
result = classifier.predict_with_pipeline("This is amazing!")

# Classify multiple texts
texts = ["Great product!", "Terrible quality."]
results = classifier.predict_with_pipeline(texts)
```

### Custom Models
```python
# Use different pre-trained models
classifier = TextClassifier("cardiffnlp/twitter-roberta-base-sentiment-latest")
result = classifier.predict_with_pipeline("I'm so excited! 🎉")
```

## Available Models

Some popular pre-trained models for text classification:

- `distilbert-base-uncased-finetuned-sst-2-english` - General sentiment analysis
- `cardiffnlp/twitter-roberta-base-sentiment-latest` - Twitter sentiment
- `nlptown/bert-base-multilingual-uncased-sentiment` - Multilingual sentiment
- `j-hartmann/emotion-english-distilroberta-base` - Emotion classification

## Fine-tuning

The `TextClassifier` class supports fine-tuning on custom data:

```python
classifier = TextClassifier("distilbert-base-uncased")

# Prepare your data
train_texts = ["positive example", "negative example", ...]
train_labels = [1, 0, ...]  # 1 = positive, 0 = negative

# Fine-tune
trainer = classifier.fine_tune(
    train_texts, train_labels,
    output_dir="./my_model",
    num_epochs=3
)
```

## Tips

1. **GPU Usage**: For faster inference and training, ensure PyTorch can use your GPU
2. **Memory**: Large models require significant memory. Use smaller models like DistilBERT for resource-constrained environments
3. **Batch Processing**: Process multiple texts together for better efficiency
4. **Model Selection**: Choose models based on your specific domain (e.g., Twitter models for social media text)

## Troubleshooting

### Common Issues

1. **Import Errors**: Make sure all dependencies are installed
2. **Memory Issues**: Try smaller models or reduce batch size
3. **Slow Performance**: Use GPU if available, or try DistilBERT models

### Performance Tips

- Use `torch.compile()` for faster inference (PyTorch 2.0+)
- Enable mixed precision training for fine-tuning
- Use appropriate batch sizes based on your hardware

## Next Steps

- Explore different model architectures
- Try domain-specific models
- Implement custom evaluation metrics
- Add data preprocessing pipelines
- Experiment with different tokenization strategies
