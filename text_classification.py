"""
Hugging Face Text Classification Examples
This module demonstrates various approaches to text classification using Hugging Face transformers.
"""

import torch
from transformers import (
    pipeline,
    AutoTokenizer,
    AutoModelForSequenceClassification,
    TrainingArguments,
    Trainer,
    DataCollatorWithPadding
)
from datasets import Dataset, load_dataset
import pandas as pd
import numpy as np
from sklearn.metrics import accuracy_score, precision_recall_fscore_support
import warnings
warnings.filterwarnings("ignore")


class TextClassifier:
    """
    A comprehensive text classification class using Hugging Face transformers.
    Supports both pre-trained models and fine-tuning.
    """

    def __init__(self, model_name="distilbert-base-uncased-finetuned-sst-2-english"):
        """
        Initialize the text classifier.

        Args:
            model_name (str): Name of the pre-trained model to use
        """
        self.model_name = model_name
        self.tokenizer = None
        self.model = None
        self.pipeline = None

    def load_pretrained_pipeline(self):
        """Load a pre-trained classification pipeline."""
        print(f"Loading pre-trained pipeline: {self.model_name}")
        self.pipeline = pipeline("text-classification", model=self.model_name)
        return self.pipeline

    def load_model_and_tokenizer(self):
        """Load model and tokenizer separately for more control."""
        print(f"Loading model and tokenizer: {self.model_name}")
        self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
        self.model = AutoModelForSequenceClassification.from_pretrained(self.model_name)
        return self.model, self.tokenizer

    def predict_with_pipeline(self, texts):
        """
        Predict using the pipeline approach.

        Args:
            texts (str or list): Text(s) to classify

        Returns:
            list: Predictions with labels and scores
        """
        if self.pipeline is None:
            self.load_pretrained_pipeline()

        if isinstance(texts, str):
            texts = [texts]

        results = self.pipeline(texts)
        return results

    def predict_with_model(self, texts):
        """
        Predict using model and tokenizer directly.

        Args:
            texts (str or list): Text(s) to classify

        Returns:
            list: Predictions with probabilities
        """
        if self.model is None or self.tokenizer is None:
            self.load_model_and_tokenizer()

        if isinstance(texts, str):
            texts = [texts]

        # Tokenize inputs
        inputs = self.tokenizer(texts, padding=True, truncation=True,
                               return_tensors="pt", max_length=512)

        # Get predictions
        with torch.no_grad():
            outputs = self.model(**inputs)
            predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)

        # Convert to readable format
        results = []
        for i, text in enumerate(texts):
            probs = predictions[i].numpy()
            predicted_class = np.argmax(probs)
            confidence = float(probs[predicted_class])

            # Map to labels (assuming binary classification for this example)
            label = "POSITIVE" if predicted_class == 1 else "NEGATIVE"

            results.append({
                "text": text,
                "label": label,
                "confidence": confidence,
                "all_scores": probs.tolist()
            })

        return results

    def prepare_dataset(self, texts, labels):
        """
        Prepare dataset for fine-tuning.

        Args:
            texts (list): List of text samples
            labels (list): List of corresponding labels

        Returns:
            Dataset: Tokenized dataset ready for training
        """
        if self.tokenizer is None:
            self.load_model_and_tokenizer()

        def tokenize_function(examples):
            return self.tokenizer(examples["text"], truncation=True, padding=True)

        # Create dataset
        dataset = Dataset.from_dict({"text": texts, "labels": labels})
        tokenized_dataset = dataset.map(tokenize_function, batched=True)

        return tokenized_dataset

    def fine_tune(self, train_texts, train_labels, val_texts=None, val_labels=None,
                  output_dir="./results", num_epochs=3, batch_size=16):
        """
        Fine-tune the model on custom data.

        Args:
            train_texts (list): Training texts
            train_labels (list): Training labels
            val_texts (list, optional): Validation texts
            val_labels (list, optional): Validation labels
            output_dir (str): Directory to save the model
            num_epochs (int): Number of training epochs
            batch_size (int): Training batch size
        """
        if self.model is None or self.tokenizer is None:
            self.load_model_and_tokenizer()

        # Prepare datasets
        train_dataset = self.prepare_dataset(train_texts, train_labels)
        val_dataset = None
        if val_texts and val_labels:
            val_dataset = self.prepare_dataset(val_texts, val_labels)

        # Data collator
        data_collator = DataCollatorWithPadding(tokenizer=self.tokenizer)

        # Training arguments
        training_args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=num_epochs,
            per_device_train_batch_size=batch_size,
            per_device_eval_batch_size=batch_size,
            warmup_steps=500,
            weight_decay=0.01,
            logging_dir=f"{output_dir}/logs",
            evaluation_strategy="epoch" if val_dataset else "no",
            save_strategy="epoch",
            load_best_model_at_end=True if val_dataset else False,
        )

        # Metrics function
        def compute_metrics(eval_pred):
            predictions, labels = eval_pred
            predictions = np.argmax(predictions, axis=1)
            precision, recall, f1, _ = precision_recall_fscore_support(labels, predictions, average='weighted')
            accuracy = accuracy_score(labels, predictions)
            return {
                'accuracy': accuracy,
                'f1': f1,
                'precision': precision,
                'recall': recall
            }

        # Initialize trainer
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=val_dataset,
            tokenizer=self.tokenizer,
            data_collator=data_collator,
            compute_metrics=compute_metrics,
        )

        # Train the model
        print("Starting training...")
        trainer.train()

        # Save the model
        trainer.save_model()
        print(f"Model saved to {output_dir}")

        return trainer


def demo_pretrained_classification():
    """Demonstrate pre-trained text classification."""
    print("=== Pre-trained Text Classification Demo ===")

    # Initialize classifier
    classifier = TextClassifier()

    # Example texts
    texts = [
        "I love this movie! It's absolutely fantastic!",
        "This is the worst film I've ever seen.",
        "The movie was okay, nothing special.",
        "Amazing acting and great storyline!",
        "I'm not sure how I feel about this movie."
    ]

    # Method 1: Using pipeline
    print("\n1. Using Pipeline:")
    results = classifier.predict_with_pipeline(texts)
    for text, result in zip(texts, results):
        print(f"Text: {text}")
        print(f"Prediction: {result['label']} (confidence: {result['score']:.3f})")
        print("-" * 50)

    # Method 2: Using model directly
    print("\n2. Using Model Directly:")
    results = classifier.predict_with_model(texts)
    for result in results:
        print(f"Text: {result['text']}")
        print(f"Prediction: {result['label']} (confidence: {result['confidence']:.3f})")
        print("-" * 50)


def demo_custom_classification():
    """Demonstrate custom text classification with different models."""
    print("\n=== Custom Model Classification Demo ===")

    # Try different models
    models = [
        "cardiffnlp/twitter-roberta-base-sentiment-latest",
        "nlptown/bert-base-multilingual-uncased-sentiment",
        "j-hartmann/emotion-english-distilroberta-base"
    ]

    text = "I'm so excited about this new opportunity!"

    for model_name in models:
        try:
            print(f"\nTesting model: {model_name}")
            classifier = TextClassifier(model_name)
            result = classifier.predict_with_pipeline(text)
            print(f"Text: {text}")
            print(f"Result: {result}")
        except Exception as e:
            print(f"Error with model {model_name}: {e}")


def demo_batch_classification():
    """Demonstrate batch text classification."""
    print("\n=== Batch Classification Demo ===")

    # Load a sample dataset
    try:
        # Using a small subset of IMDB dataset
        dataset = load_dataset("imdb", split="test[:100]")
        texts = dataset["text"][:10]  # First 10 samples

        classifier = TextClassifier()
        results = classifier.predict_with_pipeline(texts)

        print("Batch classification results:")
        for i, (text, result) in enumerate(zip(texts, results)):
            print(f"\nSample {i+1}:")
            print(f"Text: {text[:100]}...")  # First 100 chars
            print(f"Prediction: {result['label']} (confidence: {result['score']:.3f})")

    except Exception as e:
        print(f"Could not load dataset: {e}")
        print("Using sample texts instead...")

        texts = [
            "This product is amazing! Highly recommend it.",
            "Terrible quality, waste of money.",
            "Good value for the price.",
            "Not what I expected, disappointed.",
            "Perfect! Exactly what I needed."
        ]

        classifier = TextClassifier()
        results = classifier.predict_with_pipeline(texts)

        for text, result in zip(texts, results):
            print(f"Text: {text}")
            print(f"Prediction: {result['label']} (confidence: {result['score']:.3f})")
            print("-" * 40)


def demo_fine_tuning():
    """Demonstrate fine-tuning on custom data."""
    print("\n=== Fine-tuning Demo ===")

    # Sample training data (in practice, you'd have much more data)
    train_texts = [
        "This is a positive example",
        "I love this product",
        "Great quality and service",
        "This is terrible",
        "I hate this item",
        "Poor quality and bad service",
        "This is okay",
        "Not bad, could be better",
        "Average product",
        "Excellent work!",
        "Outstanding quality",
        "Disappointing experience"
    ]

    train_labels = [1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 0]  # 1 = positive, 0 = negative

    print("Note: Fine-tuning requires significant computational resources.")
    print("This is a demonstration of the code structure.")
    print("For actual fine-tuning, ensure you have:")
    print("1. Sufficient training data (thousands of examples)")
    print("2. GPU resources")
    print("3. Proper train/validation split")

    # Uncomment the following lines to actually run fine-tuning
    # classifier = TextClassifier("distilbert-base-uncased")
    # trainer = classifier.fine_tune(
    #     train_texts, train_labels,
    #     output_dir="./fine_tuned_model",
    #     num_epochs=1,  # Use more epochs in practice
    #     batch_size=8
    # )


def load_and_classify_from_file(file_path):
    """
    Load texts from a file and classify them.

    Args:
        file_path (str): Path to text file (one text per line)
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f.readlines() if line.strip()]

        classifier = TextClassifier()
        results = classifier.predict_with_pipeline(texts)

        print(f"Classification results for {file_path}:")
        for text, result in zip(texts, results):
            print(f"Text: {text}")
            print(f"Prediction: {result['label']} (confidence: {result['score']:.3f})")
            print("-" * 50)

        return results

    except FileNotFoundError:
        print(f"File {file_path} not found.")
        return None


if __name__ == "__main__":
    print("Hugging Face Text Classification Examples")
    print("=" * 50)

    # Run demonstrations
    try:
        demo_pretrained_classification()
        demo_custom_classification()
        demo_batch_classification()
        demo_fine_tuning()

        print("\n=== Additional Usage Examples ===")
        print("To classify texts from a file:")
        print("results = load_and_classify_from_file('your_texts.txt')")

        print("\nTo use different models:")
        print("classifier = TextClassifier('your-model-name')")
        print("results = classifier.predict_with_pipeline(['your text'])")

    except Exception as e:
        print(f"Error running demonstrations: {e}")
        print("Make sure you have the required packages installed:")
        print("pip install transformers torch datasets pandas scikit-learn")
