"""
Simple Hugging Face Text Classification Example
This is a minimal example to get started quickly.
"""

from transformers import pipeline

def simple_sentiment_analysis():
    """Simple sentiment analysis using pre-trained model."""
    
    # Create a sentiment analysis pipeline
    classifier = pipeline("sentiment-analysis")
    
    # Example texts to classify
    texts = [
        "I love this product!",
        "This is terrible.",
        "It's okay, nothing special.",
        "Amazing quality and fast delivery!",
        "Worst purchase ever."
    ]
    
    print("Simple Sentiment Analysis Results:")
    print("=" * 40)
    
    for text in texts:
        result = classifier(text)[0]
        print(f"Text: {text}")
        print(f"Sentiment: {result['label']}")
        print(f"Confidence: {result['score']:.3f}")
        print("-" * 40)

def batch_classification():
    """Classify multiple texts at once."""
    
    classifier = pipeline("sentiment-analysis")
    
    texts = [
        "Great product, highly recommended!",
        "Poor quality, not worth the money.",
        "Average product, meets expectations."
    ]
    
    # Classify all texts at once
    results = classifier(texts)
    
    print("\nBatch Classification Results:")
    print("=" * 40)
    
    for text, result in zip(texts, results):
        print(f"Text: {text}")
        print(f"Sentiment: {result['label']} ({result['score']:.3f})")
        print("-" * 40)

def different_models():
    """Try different pre-trained models."""
    
    models = [
        "distilbert-base-uncased-finetuned-sst-2-english",  # Default sentiment
        "cardiffnlp/twitter-roberta-base-sentiment-latest",  # Twitter sentiment
    ]
    
    text = "I'm so excited about this new opportunity!"
    
    print("\nDifferent Models Comparison:")
    print("=" * 40)
    
    for model_name in models:
        try:
            classifier = pipeline("sentiment-analysis", model=model_name)
            result = classifier(text)[0]
            print(f"Model: {model_name}")
            print(f"Text: {text}")
            print(f"Result: {result['label']} ({result['score']:.3f})")
            print("-" * 40)
        except Exception as e:
            print(f"Error with {model_name}: {e}")

if __name__ == "__main__":
    print("Hugging Face Text Classification - Simple Examples")
    print("=" * 60)
    
    # Run examples
    simple_sentiment_analysis()
    batch_classification()
    different_models()
    
    print("\nTo install required packages:")
    print("pip install transformers torch")
